import { loadStripe } from '@stripe/stripe-js';

let stripePromise: Promise<any> | null = null;

const getStripe = () => {
  if (!stripePromise && typeof window !== 'undefined') {
    stripePromise = loadStripe('pk_test_51RtvMCLhhuHU7ecW2mFSfXL4k1RUDlecA8nErWUmRUOvJhCvy3ZVxrNyAI01z5mR7h8TLpbtoq6OXi2YIrU86gm100Xxvk0iOp');
  }
  return stripePromise;
};

export const STRIPE_PRICES = {
  BASICO: 'price_1RtvdYLhhuHU7ecWyxiz6zti',
  PRO: 'price_1Rx7OBLhhuHU7ecWhotGOhi7',
  PREMIUM: 'price_1Rx7ZsLhhuHU7ecWtA3wXXXI'
};

export const redirectToCheckout = async (plan: 'BASICO' | 'PRO' | 'PREMIUM') => {
  try {
    if (typeof window === 'undefined') return;
    
    const stripePromise = getStripe();
    if (!stripePromise) return;
    
    const stripe = await stripePromise;
    if (!stripe) {
      console.error('Stripe não foi carregado');
      return;
    }

    const priceId = STRIPE_PRICES[plan];
    if (!priceId) {
      console.error('Plano inválido:', plan);
      return;
    }

    const { error } = await stripe.redirectToCheckout({
      lineItems: [{
        price: priceId,
        quantity: 1,
      }],
      mode: 'subscription',
      successUrl: `${window.location.origin}/success`,
      cancelUrl: `${window.location.origin}/cancel`,
    });

    if (error) {
      console.error('Erro no checkout:', error);
    }
  } catch (err) {
    console.error('Erro ao processar plano:', err);
  }
};